{"version": 3, "sources": ["utils.js", "detector.js", "bootstrap-navbar.js", "scroll-to-top.js", "theme.js"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "document", "readyState", "addEventListener", "setTimeout", "resize", "window", "isIterableArray", "array", "Array", "isArray", "length", "camelize", "str", "text", "replace", "_", "c", "toUpperCase", "concat", "substr", "toLowerCase", "getData", "el", "data", "JSON", "parse", "dataset", "e", "hexToRgb", "hexValue", "hex", "indexOf", "substring", "result", "exec", "m", "r", "g", "b", "parseInt", "rgbaColor", "color", "arguments", "undefined", "alpha", "colors", "primary", "secondary", "success", "info", "warning", "danger", "light", "dark", "grays", "white", "100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "1100", "black", "hasClass", "className", "classList", "value", "includes", "addClass", "add", "getOffset", "rect", "getBoundingClientRect", "scrollLeft", "pageXOffset", "documentElement", "scrollTop", "pageYOffset", "top", "left", "isScrolledIntoView", "offsetTop", "offsetLeft", "width", "offsetWidth", "height", "offsetHeight", "offsetParent", "all", "innerHeight", "innerWidth", "partial", "breakpoints", "xs", "sm", "md", "lg", "xl", "xxl", "getBreakpoint", "breakpoint", "classes", "split", "filter", "cls", "pop", "<PERSON><PERSON><PERSON><PERSON>", "name", "expire", "expires", "Date", "setTime", "getTime", "cookie", "toUTCString", "<PERSON><PERSON><PERSON><PERSON>", "keyValue", "match", "settings", "<PERSON><PERSON><PERSON>", "theme", "chart", "borderColor", "new<PERSON>hart", "config", "ctx", "getContext", "Chart", "getItemFromStore", "key", "defaultValue", "store", "localStorage", "getItem", "_unused", "setItemToStore", "payload", "setItem", "getStoreSpace", "parseFloat", "escape", "encodeURIComponent", "stringify", "toFixed", "utils", "detectorInit", "is", "html", "querySelector", "opera", "mobile", "firefox", "safari", "ios", "iphone", "ipad", "ie", "edge", "chrome", "mac", "windows", "navigator", "userAgent", "navbarInit", "windowHeight", "navbarCollapse", "colorName", "bgClassName", "paddingName", "colorRgb", "backgroundImage", "Selector", "ClassNames", "Events", "navbar", "target", "contains", "click", "allColors", "_objectSpread", "Object", "keys", "getComputedStyle", "style", "remove", "backgroundColor", "breakPoint", "transition", "scrollToTop", "querySelectorAll", "for<PERSON>ach", "anchor", "preventDefault", "id", "getAttribute", "scroll", "_utils$getData", "behavior", "location", "hash"], "mappings": "qvBAGA,IAAAA,SAAA,SAAAC,GAEA,YAAAC,SAAAC,WACAD,SAAAE,iBAAA,mBAAAH,GAEAI,WAAAJ,EAAA,IAIAK,OAAA,SAAAL,GAAA,OAAAM,OAAAH,iBAAA,SAAAH,IAEAO,gBAAA,SAAAC,GAAA,OAAAC,MAAAC,QAAAF,MAAAA,EAAAG,QAEAC,SAAA,SAAAC,GACAC,EAAAD,EAAAE,QAAA,gBAAA,SAAAC,EAAAC,GAAA,OACAA,EAAAA,EAAAC,cAAA,KAEA,MAAA,GAAAC,OAAAL,EAAAM,OAAA,EAAA,GAAAC,eAAAF,OAAAL,EAAAM,OAAA,KAGAE,QAAA,SAAAC,EAAAC,GACA,IACA,OAAAC,KAAAC,MAAAH,EAAAI,QAAAf,SAAAY,KACA,MAAAI,GACA,OAAAL,EAAAI,QAAAf,SAAAY,MAMAK,SAAA,SAAAC,GAGAC,EADA,IAAAD,EAAAE,QAAA,KACAF,EAAAG,UAAA,GACAH,EAGAI,EAAA,4CAAAC,KACAJ,EAAAhB,QAFA,mCAEA,SAAAqB,EAAAC,EAAAC,EAAAC,GAAA,OAAAF,EAAAA,EAAAC,EAAAA,EAAAC,EAAAA,KAEA,OAAAL,EACA,CACAM,SAAAN,EAAA,GAAA,IACAM,SAAAN,EAAA,GAAA,IACAM,SAAAN,EAAA,GAAA,KAEA,MAGAO,UAAA,WAAA,IAAAC,EAAA,EAAAC,UAAAhC,aAAAiC,IAAAD,UAAA,GAAAA,UAAA,GAAA,OAAAE,EAAA,EAAAF,UAAAhC,aAAAiC,IAAAD,UAAA,GAAAA,UAAA,GAAA,GAAA,MAAA,QAAAxB,OACAU,SAAAa,GADA,MAAAvB,OACA0B,EADA,MAKAC,OAAA,CACAC,QAAA,UACAC,UAAA,UACAC,QAAA,UACAC,KAAA,UACAC,QAAA,UACAC,OAAA,UACAC,MAAA,UACAC,KAAA,QAGAC,MAAA,CACAC,MAAA,OACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,IAAA,UACAC,KAAA,UACAC,MAAA,QAGAC,SAAA,SAAA9C,EAAA+C,GAEA,OAAA/C,EAAAgD,UAAAC,MAAAC,SAAAH,IAGAI,SAAA,SAAAnD,EAAA+C,GACA/C,EAAAgD,UAAAI,IAAAL,IAGAM,UAAA,SAAArD,GACA,IAAAsD,EAAAtD,EAAAuD,wBACAC,EAAAzE,OAAA0E,aAAA/E,SAAAgF,gBAAAF,WACAG,EAAA5E,OAAA6E,aAAAlF,SAAAgF,gBAAAC,UACA,MAAA,CAAAE,IAAAP,EAAAO,IAAAF,EAAAG,KAAAR,EAAAQ,KAAAN,IAGAO,mBAAA,SAAA/D,GAMA,IALA,IAAA6D,EAAA7D,EAAAgE,UACAF,EAAA9D,EAAAiE,WACAC,EAAAlE,EAAAmE,YACAC,EAAApE,EAAAqE,aAEArE,EAAAsE,cAGAT,IADA7D,EAAAA,EAAAsE,cACAN,UACAF,GAAA9D,EAAAiE,WAGA,MAAA,CACAM,IACAV,GAAA9E,OAAA6E,aACAE,GAAA/E,OAAA0E,aACAI,EAAAO,GAAArF,OAAA6E,YAAA7E,OAAAyF,aACAV,EAAAI,GAAAnF,OAAA0E,YAAA1E,OAAA0F,WACAC,QACAb,EAAA9E,OAAA6E,YAAA7E,OAAAyF,aACAV,EAAA/E,OAAA0E,YAAA1E,OAAA0F,YACAZ,EAAAO,EAAArF,OAAA6E,aACAE,EAAAI,EAAAnF,OAAA0E,cAIAkB,YAAA,CACAC,GAAA,EACAC,GAAA,IACAC,GAAA,IACAC,GAAA,IACAC,GAAA,KACAC,IAAA,MAGAC,cAAA,SAAAlF,GACA,IACAmF,EADAC,EAAApF,GAAAA,EAAAgD,UAAAC,MAYA,OATAkC,EADAC,EACAT,YACAS,EACAC,MAAA,KACAC,OAAA,SAAAC,GAAA,OAAAA,EAAArC,SAAA,oBACAsC,MACAH,MAAA,KACAG,OAGAL,GAKAM,UAAA,SAAAC,EAAAzC,EAAA0C,GACA,IAAAC,EAAA,IAAAC,KACAD,EAAAE,QAAAF,EAAAG,UAAAJ,GACAjH,SAAAsH,OAAA,GAAApG,OAAA8F,EAAA,KAAA9F,OAAAqD,EAAA,aAAArD,OAAAgG,EAAAK,gBAGAC,UAAA,SAAAR,GACAS,EAAAzH,SAAAsH,OAAAI,MAAA,UAAAxG,OAAA8F,EAAA,kBACA,OAAAS,GAAAA,EAAA,IAGAE,SAAA,CACAC,QAAA,CACAC,MAAA,SAEAC,MAAA,CACAC,YAAA,6BAMAC,SAAA,SAAAF,EAAAG,GACAC,EAAAJ,EAAAK,WAAA,MACA,OAAA,IAAA9H,OAAA+H,MAAAF,EAAAD,IAKAI,iBAAA,SAAAC,EAAAC,GAAA,IAAAC,EAAA,EAAA9F,UAAAhC,aAAAiC,IAAAD,UAAA,GAAAA,UAAA,GAAA+F,aACA,IACA,OAAAjH,KAAAC,MAAA+G,EAAAE,QAAAJ,KAAAC,EACA,MAAAI,GACA,OAAAH,EAAAE,QAAAJ,IAAAC,IAIAK,eAAA,SAAAN,EAAAO,GAAA,OAAA,EAAAnG,UAAAhC,aAAAiC,IAAAD,UAAA,GAAAA,UAAA,GAAA+F,cACAK,QAAAR,EAAAO,IACAE,cAAA,WAAA,IAAAP,EAAA,EAAA9F,UAAAhC,aAAAiC,IAAAD,UAAA,GAAAA,UAAA,GAAA+F,aAAA,OACAO,YAEAC,OAAAC,mBAAA1H,KAAA2H,UAAAX,KAAA9H,OAAA,SAEA0I,QAAA,KAGAC,MAAA,CACAvJ,SAAAA,SACAM,OAAAA,OACAE,gBAAAA,gBACAK,SAAAA,SACAU,QAAAA,QACA+C,SAAAA,SACAK,SAAAA,SACA7C,SAAAA,SACAY,UAAAA,UACAK,OAAAA,OACAS,MAAAA,MACAqB,UAAAA,UACAU,mBAAAA,mBACAmB,cAAAA,cACAO,UAAAA,UACAS,UAAAA,UACAQ,SAAAA,SACAL,SAAAA,SACAU,iBAAAA,iBACAO,eAAAA,eACAG,cAAAA,eCzNAO,aAAA,WACA,IAAAC,EAAAlJ,OAAAkJ,GACAC,EAAAxJ,SAAAyJ,cAAA,QACAF,EAAAG,SAAAjF,SAAA+E,EAAA,SACAD,EAAAI,UAAAlF,SAAA+E,EAAA,UACAD,EAAAK,WAAAnF,SAAA+E,EAAA,WACAD,EAAAM,UAAApF,SAAA+E,EAAA,UACAD,EAAAO,OAAArF,SAAA+E,EAAA,OACAD,EAAAQ,UAAAtF,SAAA+E,EAAA,UACAD,EAAAS,QAAAvF,SAAA+E,EAAA,QACAD,EAAAU,MAAAxF,SAAA+E,EAAA,MACAD,EAAAW,QAAAzF,SAAA+E,EAAA,QACAD,EAAAY,UAAA1F,SAAA+E,EAAA,UACAD,EAAAa,OAAA3F,SAAA+E,EAAA,OACAD,EAAAc,WAAA5F,SAAA+E,EAAA,WACAc,UAAAC,UAAA7C,MAAA,UAAAjD,SAAA+E,EAAA,WChBAgB,WAAA,WACA,IA8BAC,EACAjB,EACAkB,EAIAC,EAEAC,EACAC,EACAC,EACAC,EAzCAC,EAEA,mBAFAA,EAGA,kBAIAC,EACA,YAGAC,EACA,SADAA,EAEA,mBAFAA,EAGA,mBAHAA,EAIA,qBAMAC,EAAAnL,SAAAyJ,cApBA,2BAsBA0B,EAAAjL,iBAAA,QAAA,SAAAyB,GACAA,EAAAyJ,OAAA9G,UAAA+G,SAAA,aAAAhL,OAAA0F,WAAAsD,MAAA7C,cAAA2E,IACAA,EAAA1B,cAAAuB,GAAAM,UAIAH,IACAV,EAAApK,OAAAyF,YACA0D,EAAAxJ,SAAAgF,gBACA0F,EAAAS,EAAA1B,cAAAuB,GACAO,EAAAC,cAAAA,cAAA,GAAAnC,MAAAxG,QAAAwG,MAAA/F,OAEA0D,EAAAqC,MAAAhI,QAAA8J,EAhBA,0BAkBA1I,EAAA8I,EADAZ,EAAAc,OAAAC,KAAAH,GAAA/G,SAAAwC,GAAAA,EAAA,SAEA4D,EAAA,MAAA1J,OAAAyJ,GACAE,EAAA,qBACAC,EAAAzB,MAAAzH,SAAAa,GACAsI,EAAA1K,OAAAsL,iBAAAR,GAAAJ,gBAEAI,EAAAS,MAAAb,gBAAA,OAGA1K,OAAAH,iBAAAgL,EAAA,WACA,IACAtI,EADA4G,EAAAvE,UACAwF,EAAA,IAEAU,EAAA7G,UAAAI,IAAA,YACA,IAAA9B,GACAuI,EAAA7G,UAAAuH,OAAA,YAEA,GAAAjJ,IAAAA,EAAA,GACAuI,EAAAS,MAAAE,gBAAA,QAAA5K,OAAA4J,EAAA,GAAA,MAAA5J,OAAA4J,EAAA,GAAA,MAAA5J,OAAA4J,EAAA,GAAA,MAAA5J,OAAA0B,EAAA,KACAuI,EAAAS,MAAAb,gBAAA,EAAAnI,GAAAyG,MAAAjF,SAAAsG,EAAA,QAAAK,EAAA,OACA,EAAAnI,GAAAyG,MAAAjF,SAAAsG,EAAA,QAAAS,EAAA7G,UAAAI,IAAAmG,GAAAM,EAAA7G,UAAAuH,OAAAhB,KAIAxB,MAAAjJ,OAAA,WACA,IAAA2L,EAAA1C,MAAA7C,cAAA2E,GACA9K,OAAA0F,WAAAgG,EACAZ,EAAAS,MAAAb,gBAAAvB,EAAAvE,UAAA8F,EAAA,OAGA1B,MAAAjF,SACA+G,EAAA1B,cAAAuB,GACAC,KAKAE,EAAAS,MAAAb,gBAAAA,GAGA1K,OAAA0F,aAMA2E,EAAAxK,iBAAAgL,EAAA,WACAC,EAAA7G,UAAAI,IAAAkG,GAEAO,EAAAS,MAAAb,gBAAAA,EACAI,EAAAS,MAAAI,WA7CA,wCAgDAtB,EAAAxK,iBAAAgL,EAAA,WACAC,EAAA7G,UAAAuH,OAAAjB,GAEApB,EAAAvE,YAAAkG,EAAAS,MAAAb,gBAAA,UAGAL,EAAAxK,iBAAAgL,EAAA,gBCjGAe,YAAA,WACAjM,SACAkM,iBAAA,uCACAC,QAAA,SAAAC,GACAA,EAAAlM,iBAAA,QAAA,SAAAyB,GACAA,EAAA0K,iBACA,IAAA/K,EAAAK,EAAAyJ,OACAkB,EAAAjD,MAAAhI,QAAAC,EAAA,cAAAA,EAAAiL,aAAA,QACAlM,OAAAmM,OAAA,CACArH,IAAA,QAAAsH,EACApD,MAAAhI,QAAAC,EAAA,qBADA,IAAAmL,EAAAA,EAEApD,MAAA1E,UAAA3E,SAAAyJ,cAAA6C,IAAAnH,IAAA,IACAC,KAAA,EACAsH,SAAA,WAEArM,OAAAsM,SAAAC,KAAAN,OCVAxM,SAAA0K,YJRA1K,SAAAwJ,cIUAxJ,SAAAmM", "file": "theme.min.js", "sourcesContent": ["/* -------------------------------------------------------------------------- */\r\n/*                                    Utils                                   */\r\n/* -------------------------------------------------------------------------- */\r\nconst docReady = (fn) => {\r\n  // see if DOM is already available\r\n  if (document.readyState === \"loading\") {\r\n    document.addEventListener(\"DOMContentLoaded\", fn);\r\n  } else {\r\n    setTimeout(fn, 1);\r\n  }\r\n};\r\n\r\nconst resize = (fn) => window.addEventListener(\"resize\", fn);\r\n\r\nconst isIterableArray = (array) => Array.isArray(array) && !!array.length;\r\n\r\nconst camelize = (str) => {\r\n  const text = str.replace(/[-_\\s.]+(.)?/g, (_, c) =>\r\n    c ? c.toUpperCase() : \"\"\r\n  );\r\n  return `${text.substr(0, 1).toLowerCase()}${text.substr(1)}`;\r\n};\r\n\r\nconst getData = (el, data) => {\r\n  try {\r\n    return JSON.parse(el.dataset[camelize(data)]);\r\n  } catch (e) {\r\n    return el.dataset[camelize(data)];\r\n  }\r\n};\r\n\r\n/* ----------------------------- Colors function ---------------------------- */\r\n\r\nconst hexToRgb = (hexValue) => {\r\n  let hex;\r\n  hexValue.indexOf(\"#\") === 0\r\n    ? (hex = hexValue.substring(1))\r\n    : (hex = hexValue);\r\n  // Expand shorthand form (e.g. \"03F\") to full form (e.g. \"0033FF\")\r\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(\r\n    hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b)\r\n  );\r\n  return result\r\n    ? [\r\n        parseInt(result[1], 16),\r\n        parseInt(result[2], 16),\r\n        parseInt(result[3], 16),\r\n      ]\r\n    : null;\r\n};\r\n\r\nconst rgbaColor = (color = \"#fff\", alpha = 0.5) =>\r\n  `rgba(${hexToRgb(color)}, ${alpha})`;\r\n\r\n/* --------------------------------- Colors --------------------------------- */\r\n\r\nconst colors = {\r\n  primary: \"#0091e9\",\r\n  secondary: \"#002147\",\r\n  success: \"#00d27a\",\r\n  info: \"#27bcfd\",\r\n  warning: \"#FFC928\",\r\n  danger: \"#EE4D47\",\r\n  light: \"#F9FAFD\",\r\n  dark: \"#000\",\r\n};\r\n\r\nconst grays = {\r\n  white: \"#fff\",\r\n  100: \"#f9fafd\",\r\n  200: \"#edf2f9\",\r\n  300: \"#d8e2ef\",\r\n  400: \"#b6c1d2\",\r\n  500: \"#9da9bb\",\r\n  600: \"#748194\",\r\n  700: \"#5e6e82\",\r\n  800: \"#4d5969\",\r\n  900: \"#344050\",\r\n  1000: \"#232e3c\",\r\n  1100: \"#0b1727\",\r\n  black: \"#000\",\r\n};\r\n\r\nconst hasClass = (el, className) => {\r\n  !el && false;\r\n  return el.classList.value.includes(className);\r\n};\r\n\r\nconst addClass = (el, className) => {\r\n  el.classList.add(className);\r\n};\r\n\r\nconst getOffset = (el) => {\r\n  const rect = el.getBoundingClientRect();\r\n  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\r\n  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n  return { top: rect.top + scrollTop, left: rect.left + scrollLeft };\r\n};\r\n\r\nconst isScrolledIntoView = (el) => {\r\n  let top = el.offsetTop;\r\n  let left = el.offsetLeft;\r\n  const width = el.offsetWidth;\r\n  const height = el.offsetHeight;\r\n\r\n  while (el.offsetParent) {\r\n    // eslint-disable-next-line no-param-reassign\r\n    el = el.offsetParent;\r\n    top += el.offsetTop;\r\n    left += el.offsetLeft;\r\n  }\r\n\r\n  return {\r\n    all:\r\n      top >= window.pageYOffset &&\r\n      left >= window.pageXOffset &&\r\n      top + height <= window.pageYOffset + window.innerHeight &&\r\n      left + width <= window.pageXOffset + window.innerWidth,\r\n    partial:\r\n      top < window.pageYOffset + window.innerHeight &&\r\n      left < window.pageXOffset + window.innerWidth &&\r\n      top + height > window.pageYOffset &&\r\n      left + width > window.pageXOffset,\r\n  };\r\n};\r\n\r\nconst breakpoints = {\r\n  xs: 0,\r\n  sm: 576,\r\n  md: 768,\r\n  lg: 992,\r\n  xl: 1200,\r\n  xxl: 1540,\r\n};\r\n\r\nconst getBreakpoint = (el) => {\r\n  const classes = el && el.classList.value;\r\n  let breakpoint;\r\n  if (classes) {\r\n    breakpoint =\r\n      breakpoints[\r\n        classes\r\n          .split(\" \")\r\n          .filter((cls) => cls.includes(\"navbar-expand-\"))\r\n          .pop()\r\n          .split(\"-\")\r\n          .pop()\r\n      ];\r\n  }\r\n  return breakpoint;\r\n};\r\n\r\n/* --------------------------------- Cookie --------------------------------- */\r\n\r\nconst setCookie = (name, value, expire) => {\r\n  const expires = new Date();\r\n  expires.setTime(expires.getTime() + expire);\r\n  document.cookie = name + \"=\" + value + \";expires=\" + expires.toUTCString();\r\n};\r\n\r\nconst getCookie = (name) => {\r\n  var keyValue = document.cookie.match(\"(^|;) ?\" + name + \"=([^;]*)(;|$)\");\r\n  return keyValue ? keyValue[2] : keyValue;\r\n};\r\n\r\nconst settings = {\r\n  tinymce: {\r\n    theme: \"oxide\",\r\n  },\r\n  chart: {\r\n    borderColor: \"rgba(255, 255, 255, 0.8)\",\r\n  },\r\n};\r\n\r\n/* -------------------------- Chart Initialization -------------------------- */\r\n\r\nconst newChart = (chart, config) => {\r\n  const ctx = chart.getContext(\"2d\");\r\n  return new window.Chart(ctx, config);\r\n};\r\n\r\n/* ---------------------------------- Store --------------------------------- */\r\n\r\nconst getItemFromStore = (key, defaultValue, store = localStorage) => {\r\n  try {\r\n    return JSON.parse(store.getItem(key)) || defaultValue;\r\n  } catch {\r\n    return store.getItem(key) || defaultValue;\r\n  }\r\n};\r\n\r\nconst setItemToStore = (key, payload, store = localStorage) =>\r\n  store.setItem(key, payload);\r\nconst getStoreSpace = (store = localStorage) =>\r\n  parseFloat(\r\n    (\r\n      escape(encodeURIComponent(JSON.stringify(store))).length /\r\n      (1024 * 1024)\r\n    ).toFixed(2)\r\n  );\r\n\r\nconst utils = {\r\n  docReady,\r\n  resize,\r\n  isIterableArray,\r\n  camelize,\r\n  getData,\r\n  hasClass,\r\n  addClass,\r\n  hexToRgb,\r\n  rgbaColor,\r\n  colors,\r\n  grays,\r\n  getOffset,\r\n  isScrolledIntoView,\r\n  getBreakpoint,\r\n  setCookie,\r\n  getCookie,\r\n  newChart,\r\n  settings,\r\n  getItemFromStore,\r\n  setItemToStore,\r\n  getStoreSpace,\r\n};\r\nexport default utils;\r\n", "import { addClass } from './utils';\r\n/* -------------------------------------------------------------------------- */\r\n/*                                  Detector                                  */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst detectorInit = () => {\r\n  const { is } = window;\r\n  const html = document.querySelector('html');\r\n  is.opera() && addClass(html, 'opera');\r\n  is.mobile() && addClass(html, 'mobile');\r\n  is.firefox() && addClass(html, 'firefox');\r\n  is.safari() && addClass(html, 'safari');\r\n  is.ios() && addClass(html, 'ios');\r\n  is.iphone() && addClass(html, 'iphone');\r\n  is.ipad() && addClass(html, 'ipad');\r\n  is.ie() && addClass(html, 'ie');\r\n  is.edge() && addClass(html, 'edge');\r\n  is.chrome() && addClass(html, 'chrome');\r\n  is.mac() && addClass(html, 'osx');\r\n  is.windows() && addClass(html, 'windows');\r\n  navigator.userAgent.match('CriOS') && addClass(html, 'chrome');\r\n\r\n};\r\n\r\nexport default detectorInit;\r\n", "import utils from './utils';\r\n/*-----------------------------------------------\r\n|   Top navigation opacity on scroll\r\n-----------------------------------------------*/\r\nconst navbarInit = () => {\r\n  const Selector = {\r\n    NAVBAR: '[data-navbar-on-scroll]',\r\n    NAVBAR_COLLAPSE: '.navbar-collapse',\r\n    NAVBAR_TOGGLER: '.navbar-toggler',\r\n\r\n  };\r\n\r\n  const ClassNames = {\r\n    COLLAPSED: 'collapsed',\r\n  };\r\n\r\n  const Events = {\r\n    SCROLL: 'scroll',\r\n    SHOW_BS_COLLAPSE: 'show.bs.collapse',\r\n    HIDE_BS_COLLAPSE: 'hide.bs.collapse',\r\n    HIDDEN_BS_COLLAPSE: 'hidden.bs.collapse',\r\n  };\r\n\r\n  const DataKey = {\r\n    NAVBAR_ON_SCROLL: 'navbar-light-on-scroll'\r\n  };\r\n  const navbar = document.querySelector(Selector.NAVBAR);\r\n  // responsive nav collapsed\r\n  navbar.addEventListener('click', (e) => {\r\n    if (e.target.classList.contains('nav-link') && window.innerWidth < utils.getBreakpoint(navbar)) {\r\n      navbar.querySelector(Selector.NAVBAR_TOGGLER).click();\r\n    }\r\n  });\r\n\r\n  if (navbar) {\r\n    const windowHeight = window.innerHeight;\r\n    const html = document.documentElement;\r\n    const navbarCollapse = navbar.querySelector(Selector.NAVBAR_COLLAPSE);\r\n    const allColors = { ...utils.colors, ...utils.grays };\r\n\r\n    const name = utils.getData(navbar, DataKey.NAVBAR_ON_SCROLL);\r\n    const colorName = Object.keys(allColors).includes(name) ? name : 'light';\r\n    const color = allColors[colorName];\r\n    const bgClassName = `bg-${colorName}`;\r\n    const paddingName = 'padding-transition';\r\n    const colorRgb = utils.hexToRgb(color);\r\n    const { backgroundImage } = window.getComputedStyle(navbar);\r\n    const transition = 'background-color,padding 0.35s ease';\r\n    navbar.style.backgroundImage = 'none';\r\n\r\n    // Change navbar background color on scroll\r\n    window.addEventListener(Events.SCROLL, () => {\r\n      const { scrollTop } = html;\r\n      let alpha = (scrollTop / windowHeight) * 0.35;\r\n      // Add class on scroll\r\n      navbar.classList.add('backdrop');\r\n      if (alpha === 0) {\r\n        navbar.classList.remove('backdrop');\r\n      }\r\n      alpha >= 1 && (alpha = 1);\r\n      navbar.style.backgroundColor = `rgba(${colorRgb[0]}, ${colorRgb[1]}, ${colorRgb[2]}, ${alpha})`;\r\n      navbar.style.backgroundImage = (alpha > 0 || utils.hasClass(navbarCollapse, 'show')) ? backgroundImage : 'none';\r\n      (alpha > 0 || utils.hasClass(navbarCollapse, 'show')) ? navbar.classList.add(paddingName) : navbar.classList.remove(paddingName);\r\n    });\r\n\r\n    // Toggle bg class on window resize\r\n    utils.resize(() => {\r\n      const breakPoint = utils.getBreakpoint(navbar);\r\n      if (window.innerWidth > breakPoint) {\r\n        navbar.style.backgroundImage = html.scrollTop ? backgroundImage : 'none';\r\n        // navbar.style.transition = 'none';\r\n      } else if (\r\n        !utils.hasClass(\r\n          navbar.querySelector(Selector.NAVBAR_TOGGLER),\r\n          ClassNames.COLLAPSED\r\n        )\r\n      ) {\r\n        // navbar.classList.add(bgClassName);\r\n        // navbar.classList.add(paddingName);\r\n        navbar.style.backgroundImage = backgroundImage;\r\n      }\r\n\r\n      if (window.innerWidth <= breakPoint) {\r\n        // navbar.style.transition = utils.hasClass(navbarCollapse, 'show') ? transition : 'none';\r\n      }\r\n\r\n    });\r\n\r\n    navbarCollapse.addEventListener(Events.SHOW_BS_COLLAPSE, () => {\r\n      navbar.classList.add(bgClassName);\r\n      // navbar.classList.add(paddingName);\r\n      navbar.style.backgroundImage = backgroundImage;\r\n      navbar.style.transition = transition;\r\n    });\r\n\r\n    navbarCollapse.addEventListener(Events.HIDE_BS_COLLAPSE, () => {\r\n      navbar.classList.remove(bgClassName);\r\n      // navbar.classList.remove(paddingName);\r\n      !html.scrollTop && (navbar.style.backgroundImage = 'none');\r\n    });\r\n\r\n    navbarCollapse.addEventListener(Events.HIDDEN_BS_COLLAPSE, () => {\r\n      // navbar.style.transition = 'none';\r\n    });\r\n\r\n  }\r\n\r\n};\r\n\r\nexport default navbarInit;\r\n", "import utils from './utils';\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Scroll To Top                               */\r\n/* -------------------------------------------------------------------------- */\r\nconst scrollToTop = () => {\r\n  document\r\n    .querySelectorAll('[data-anchor] > a, [data-scroll-to]')\r\n    .forEach((anchor) => {\r\n      anchor.addEventListener('click', (e) => {\r\n        e.preventDefault();\r\n        const el = e.target;\r\n        const id = utils.getData(el, 'scroll-to') || el.getAttribute('href');\r\n        window.scroll({\r\n          top:\r\n            utils.getData(el, 'offset-top') ??\r\n            utils.getOffset(document.querySelector(id)).top - 100,\r\n          left: 0,\r\n          behavior: 'smooth',\r\n        });\r\n        window.location.hash = id;\r\n      });\r\n    });\r\n};\r\nexport default scrollToTop;", "import { docReady } from './utils';\r\nimport navbarInit from './bootstrap-navbar';\r\nimport detectorInit from './detector';\r\nimport scrollToTop from './scroll-to-top';\r\n\r\n// /* -------------------------------------------------------------------------- */\r\n// /*                            Theme Initialization                            */\r\n// /* -------------------------------------------------------------------------- */\r\n\r\ndocReady(navbarInit);\r\ndocReady(detectorInit);\r\ndocReady(scrollToTop);\r\n"]}