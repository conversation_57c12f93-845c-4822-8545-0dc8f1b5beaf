version: "3.7"

services:
  resume-dev-service:
    container_name: resume-dev
    build:
      context: ..
      dockerfile: deploy/Dockerfile.dev
    ports:
      - "8888:8888"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    env_file:
      - ../.env

  resume-prod-service:
    container_name: resume-prod
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    ports:
      - "8888:8888"
    environment:
      - NODE_ENV=production
    env_file:
      - ../.env
