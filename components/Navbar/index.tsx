"use client";

import { useEffect, useState } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';


interface Settings {
  displayName: string | null | undefined;
  defaultTemplate: string;
}

interface NavLink {
  title: string;
  href: string;
}

const navLinks: NavLink[] = [
  { title: 'My Dashboard', href: '/profile' },
  { title: 'Create Resume', href: '/resume/create' },
];

export default function Navbar() {
  const { data: session } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [settings, setSettings] = useState<Settings>({
    displayName: '',
    defaultTemplate: 'modern'
  });
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
    setSettings({
      displayName: window.localStorage.getItem("resumeitnow_name") || session?.user?.name,
      defaultTemplate: window.localStorage.getItem("resumeitnow_template") || 'modern'
    });
  }, [session]);

  if (!mounted) return null;

  const handleSignOut = async () => {
    localStorage.clear();
    await signOut({ redirect: false });
    router.push('/');
  };

  const navigateTo = (href: string) => {
    router.push(href);
  };

  const UserMenu = () => (
    <>
    <ul className="flex items-center gap-6">
      {navLinks.map((link) => (
        <li key={link.href}>
          <a  className="font-bold text-gray-800 hover:text-red-700 cursor-pointer transition" onClick={() => navigateTo(link.href)}>
            {link.title}
          </a>
        </li>
      ))}
    </ul>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-full md:w-auto">
          {settings.displayName || session?.user?.name || 'User'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem className="cursor-pointer" onClick={() => navigateTo('/profile')}>
            Profile
          </DropdownMenuItem> */}
          <DropdownMenuItem className="cursor-pointer" onClick={() => navigateTo('/settings')}>
            Settings
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-red-400 cursor-pointer" onClick={handleSignOut}>
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    </>

  );

    const UserMobileMenu = () => (
    <>
      <ul className="flex flex-col p-4 space-y-2">
      {navLinks.map((link) => (
          <li key={link.href}>
            <a className="font-bold text-gray-800 hover:text-red-700" onClick={() => navigateTo(link.href)}>
              {link.title}
            </a>
          </li>
      ))}
    </ul>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-full md:w-auto">
          {settings.displayName || session?.user?.name || 'User'}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem className="cursor-pointer" onClick={() => navigateTo('/profile')}>
            Profile
          </DropdownMenuItem> */}
          <DropdownMenuItem className="cursor-pointer" onClick={() => navigateTo('/settings')}>
            Settings
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-red-400 cursor-pointer" onClick={handleSignOut}>
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    </>

  );

  return (
    <nav className="fixed top-0 left-0 w-full z-50 bg-white/80 shadow transition-all">
      <div className="container mx-auto flex items-center justify-between py-4 px-4 md:px-8">
        <a className="flex items-center gap-2" href={process.env.NEXT_PUBLIC_BASE_URL}>
          <img src={`${process.env.NEXT_PUBLIC_BASE_URL}/assets/img/techgig-logo.svg`} alt="logo" className="h-8" />
        </a>

        {/* Hamburger Icon */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="md:hidden focus:outline-none text-gray-800"
          aria-label="Toggle Menu"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            {isOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>

        {/* Desktop Menu */}
        <div className="hidden md:flex items-center gap-6">
          
          {(session && pathname !== '/') ? <UserMenu /> : 
          <ul className="flex items-center gap-6">
            <li>
              <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#home">
                Home
              </a>
            </li>
            <li>
              <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#service">
                Features
              </a>
            </li>
            <li>
              <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#testimonial">
                Testimonials
              </a>
            </li>
            <li>
              <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#resume">
                Create Resume
              </a>
            </li>
          </ul>
          }
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden transition-all duration-300 overflow-hidden bg-white shadow ${
          isOpen ? 'max-h-60' : 'max-h-0'
        }`}
      >
        {(session && pathname !== '/') ? <UserMobileMenu /> : 
        <ul className="flex flex-col p-4 space-y-2">
          <li>
            <a className="font-bold text-gray-800 hover:text-yellow-500" href="#home">
              Home
            </a>
          </li>
          <li>
            <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#service">
              Features
            </a>
          </li>
          <li>
            <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#testimonial">
              Testimonials
            </a>
          </li>
          <li>
            <a className="font-bold text-gray-800 hover:text-yellow-500 transition" href="#resume">
              Create Resume
            </a>
          </li>
        </ul>
        }
        {/* {(session && pathname !== '/') && <UserMenu />} */}
      </div>
    </nav>
  );
}