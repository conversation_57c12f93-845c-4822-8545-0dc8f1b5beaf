"use client";
import React from "react";

interface FooterLink {
   label: string;
   href: string;
}

interface FooterSection {
   title: string;
   links: FooterLink[];
}

const jobLinks: FooterSection = {
   title: 'Jobs:',
   links: [
      {label:"PHP DeveloperJobs", href:"/jobs/designation/PHP-Developer"},
      {label:"Java Developer Jobs", href:"/jobs/designation/Java-Developer"},
      {label:"Oracle DBA Jobs", href:"/jobs/designation/Oracle-DBA"},
      {label:"Network EngineerJobs", href:"/jobs/designation/Network-Engineer"},
      {label:"Linux Administrator Jobs", href:"/jobs/designation/Linux-Administrator"},
      {label:"Web DesignerJobs", href:"/jobs/designation/Web-Designer"},
      {label:"SQLServer DBA Jobs", href:"/jobs/designation/SQLServer-DBA"},
      {label:"AllDesignation", href:"/jobs/designation"},
      {label:"Jobs In Delhi", href:"/jobs/city/Delhi-Jobs"},
      {label:"Jobs In Mumbai", href:"/jobs/city/Mumbai-Jobs"},
      {label:"Jobs In Chennai", href:"/jobs/city/Chennai-Jobs"},
      {label:"Jobs In Gurgaon", href:"/jobs/city/Gurgaon-Jobs"},
      {label:"Jobs In Bangalore", href:"/jobs/city/Bangalore-Jobs"},
      {label:"Jobs In Noida", href:"/jobs/city/Noida-Jobs"},
      {label:"Jobs In Hyderabad", href:"/jobs/city/Hyderabad-Jobs"},
      {label:"All Cities", href:"/jobs/city"},
   ]
}

const skillPageLinks: FooterSection = {
   title: 'Follow Skill Pages:',
   links: [
      {label:"Java", href:"/skill/java"},
      {label:"Android", href:"/skill/android"},
      {label:"Big Data", href:"/skill/bigdata"},
      {label:"C# Programming", href:"/skill/csharp"},
      {label:"Cloud computing", href:"/skill/cloudcomputing"},
      {label:"Project Management", href:"/skill/projectmanagement"},
      {label:"Software Testing", href:"/skill/softwaretesting"},
      {label:"All Skill Pages", href:"/skill"},
   ]
}

const skillTestLinks: FooterSection = {
   title: 'Skill Test:',
   links: [
      {label:"Java Test", href:"/skilltest/java"},
      {label:".Net Test", href:"/skilltest/.net"},
      {label:"C Test", href:"/skilltest/c"},
      {label:"HTML Test", href:"/skilltest/html"},
      {label:"Javascript Test", href:"/skilltest/javascript"},
   ]
}

const problemLinks: FooterSection = {
   title: 'Problem/ Tutorial:',
   links: [
      {label:"Java Problems", href:"/practice/java"},
      {label:"Python Problems", href:"/practice/python"},
      {label:"C++ Problems", href:"/practice/cpp"},
      {label:"Regex Problems", href:"/practice/regex"},
      {label:"Data Structure Tutorial", href:"/practice/data-structure"},
      {label:"Programming Tutorials", href:"/practice"},
   ]
}

const newsLinks: FooterSection = {
   title: 'News Widgets:',
   links: [
      {label:"Latest News", href:"/"},
      {label:"Top Tech News", href:"/#top-technews"},
      {label:"Technology News", href:"/technology"},
      {label:"Hiring News", href:"/hiring"},
      {label:"Internet News", href:"/internet"},
      {label:"Gadgets News", href:"/gadgets"},
      {label:"Mobile Technologies News", href:"/mobile"},
      {label:"Startups News", href:"/startups"},
      {label:"Social Media News", href:"/social-media"},
      {label:"It-Security News", href:"/it-security"},
      {label:"Ecommerce News", href:"/ecommerce"},
      {label:"Leadership News", href:"/leadership"},
   ]
}

const FooterRelatedLinks = ({section} : {section:FooterSection}) =>(
   <p>
      <strong>{section.title}</strong>
      {section.links.map((link,index)=>(
         <React.Fragment key={link.href}>
            <a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}${link.href}`}>{link.label}</a>
            {index < section.links.length -1 && ' | '}
         </React.Fragment>

      ))}
   </p>
);

export function Footer() {

  return (
   
      <footer id="footer">
         <div id="footer-related-links">
            <div className="container">
               <FooterRelatedLinks section={jobLinks} />
               <FooterRelatedLinks section={skillPageLinks} />
               <FooterRelatedLinks section={skillTestLinks} />
               <FooterRelatedLinks section={problemLinks} />
               <FooterRelatedLinks section={newsLinks} />
            </div>
         </div>
   

      <div className="container">
         <div className="row align-items-center inner-main-container">
            <div className="col-sm-6 mrg16">
               <img src={`${process.env.NEXT_PUBLIC_TG_URL}/files/contest_upload_files/techgig-white-logo-141024.svg`}
                     width="117" loading="lazy" alt="TechGig"/>
            </div>
            <div className="col-sm-6 mrg16">
               <div className="footer-sociable clearfix">
                     <div>Follow us on</div>
                     <a href="https://www.facebook.com/Techgig" target="_blank" aria-label="Facebook"
                        className="facebook ctrlcenter" rel="nofollow">
                        <svg width="7px" height="16px" viewBox="0 0 7 16" version="1.1"
                           xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                           <defs></defs>
                           <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                                 <g id="Fb" fill="#FFFFFF" fillRule="nonzero">
                                    <path
                                       d="M1.55086906,16 L4.65504056,16 L4.65504056,7.99913616 L6.72584009,7.99913616 L7,5.24263039 L4.65504056,5.24263039 L4.65828505,3.8622179 C4.65828505,3.14350502 4.72236385,2.75823345 5.69084589,2.75823345 L6.98539977,2.75823345 L6.98539977,0 L4.91378911,0 C2.42607184,0 1.55086906,1.33808444 1.55086906,3.58665371 L1.55086906,5.24176655 L0,5.24176655 L0,7.99913616 L1.55086906,7.99913616 L1.55086906,16 L1.55086906,16 Z">
                                    </path>
                                 </g>
                           </g>
                        </svg>
                     </a>
                     <a href="https://twitter.com/techgigdotcom" target="_blank" aria-label="Twitter"
                        className="twitter ctrlcenter" rel="nofollow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15"
                           fill="none">
                           <path
                                 d="M0.038933 0L6.21635 8.25964L0 14.9751H1.39916L6.84168 9.09549L11.2389 14.9751H16L9.47488 6.25098L15.2611 0H13.8619L8.84981 5.41487L4.8 0H0.038933ZM2.09646 1.03051H4.28367L13.9422 13.9446H11.755L2.09646 1.03051Z"
                                 fill="white"></path>
                        </svg>
                     </a>
                     <a href="https://www.linkedin.com/company/techgig-com" aria-label="LinkedIn" target="_blank"
                        className="linkedin ctrlcenter" rel="nofollow">
                        <svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1"
                           xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                           <defs></defs>
                           <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                                 <g id="Linkedin" fill="#FFFFFF" fillRule="nonzero">
                                    <path
                                       d="M0.*********,4.64864865 L3.13923077,4.64864865 L3.13923077,14 L0.*********,14 L0.*********,4.64864865 Z M1.69615385,0 C2.62230769,0 3.37615385,0.********* 3.37615385,1.68648649 C3.37615385,2.61621622 2.62230769,3.37297297 1.69615385,3.37297297 C0.*********,3.37297297 0.0161538462,2.61621622 0.0161538462,1.68648649 C0.0161538462,0.********* 0.*********,0 1.69615385,0 Z M4.95923077,4.64864865 L7.73230769,4.64864865 L7.73230769,5.92972973 L7.77,5.92972973 C8.15769231,5.19459459 9.1,4.42162162 10.5107692,4.42162162 C13.44,4.42162162 13.9838462,6.35675676 13.9838462,8.87567568 L13.9838462,14 L11.0923077,14 L11.0923077,9.45405405 C11.0923077,8.36756757 11.0761538,6.97297297 9.59,6.97297297 C8.08230769,6.97297297 7.85615385,8.15675676 7.85615385,9.37297297 L7.85615385,14 L4.96461538,14 L4.95923077,4.64864865 Z">
                                    </path>
                                 </g>
                           </g>
                        </svg>
                     </a>
                     <a href="https://www.instagram.com/techgigdotcom/" aria-label="Instagram" target="_blank"
                        className="linkedin ctrlcenter">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" height="14" width="14">
                           <path fill="#fff"
                                 d="M10.668,16A5.333,5.333,0,1,1,16,21.334,5.333,5.333,0,0,1,10.668,16M7.784,16A8.216,8.216,0,1,0,16,7.784,8.216,8.216,0,0,0,7.784,16M22.622,7.458a1.921,1.921,0,1,0,1.921-1.919,1.921,1.921,0,0,0-1.921,1.919M9.536,29.024a8.839,8.839,0,0,1-2.971-.551,4.977,4.977,0,0,1-1.84-1.2,4.945,4.945,0,0,1-1.2-1.839,8.838,8.838,0,0,1-.551-2.971C2.9,20.779,2.883,20.273,2.883,16s.017-4.778.093-6.466a8.879,8.879,0,0,1,.551-2.971,4.977,4.977,0,0,1,1.2-1.84,4.94,4.94,0,0,1,1.84-1.2,8.839,8.839,0,0,1,2.971-.551C11.222,2.9,11.729,2.882,16,2.882s4.778.017,6.466.093a8.88,8.88,0,0,1,2.971.551,4.961,4.961,0,0,1,1.84,1.2,4.966,4.966,0,0,1,1.2,1.84,8.838,8.838,0,0,1,.551,2.971c.078,1.688.093,2.193.093,6.466s-.016,4.778-.093,6.466a8.871,8.871,0,0,1-.551,2.971,5.3,5.3,0,0,1-3.037,3.036,8.839,8.839,0,0,1-2.971.551c-1.687.078-2.193.093-6.466.093s-4.778-.016-6.465-.093M9.4.1A11.737,11.737,0,0,0,5.52.84,7.852,7.852,0,0,0,2.686,2.686,7.829,7.829,0,0,0,.84,5.52,11.736,11.736,0,0,0,.1,9.4C.018,11.11,0,11.655,0,16s.018,4.89.1,6.6A11.736,11.736,0,0,0,.84,26.48a7.823,7.823,0,0,0,1.846,2.834A7.847,7.847,0,0,0,5.52,31.16,11.744,11.744,0,0,0,9.4,31.9c1.707.078,2.251.1,6.6.1s4.891-.018,6.6-.1a11.737,11.737,0,0,0,3.884-.743,8.181,8.181,0,0,0,4.68-4.68A11.7,11.7,0,0,0,31.9,22.6c.078-1.707.1-2.251.1-6.6s-.018-4.89-.1-6.6a11.736,11.736,0,0,0-.743-3.884,7.868,7.868,0,0,0-1.846-2.834A7.847,7.847,0,0,0,26.482.84,11.718,11.718,0,0,0,22.6.1C20.893.019,20.347,0,16,0S11.111.018,9.4.1"
                                 id="instagram"></path>
                        </svg>
                     </a>
                     <a href="https://www.youtube.com/@TechGigdotcom" aria-label="YouTube" target="_blank"
                        className="linkedin ctrlcenter">
                        <img src={`${process.env.NEXT_PUBLIC_TG_URL}/files/contest_upload_files/footer-yt-031024.png`}
                           alt="YouTube"/>
                     </a>
               </div>
            </div>
         </div>
            
         <div className="row mrg24 mrgb16 outer-footer-container">
            <div className="col-sm-7">
               <div className="row footer-link-container">
                  <div className="col-sm-3">
                  <h4 className="dev-head text-uppercase">COMPANY</h4>
                  <ul className="nav-links">
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/knowus/about-us`}>About Us</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/knowus/contact-us#tg-contact`}>Contact Us</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/knowus/in-the-press#in-press`}>In The Press</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/user/privacy`}>Privacy Policy</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/user/cookiepolicy`}>Cookie Policy</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/knowus/our-awards#our-awards`}>Our Awards</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/user/terms`}>Terms &amp; Conditions</a></li>
                     <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/upload-resume`}>Join Our Team</a></li>
                  </ul>
                  </div>
                  <div className="col-sm-6">
                  <div className="row">
                     <div className="col-12">
                        <h4 className="dev-head text-uppercase">FOR TECH COMMUNITY</h4>
                     </div>
                     <div className="col-6">
                        <ul className="nav-links fullbox">
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/tgpro-profile`}>Create Your Tech Profile</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/challenge`}>Tech Challenges</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/skilltest`}>Assess Your Skills</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/practice`}>Practice Problems</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/codegladiators`}>Code Gladiators</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/geekgoddess`}>Geek Goddess</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/get-hired`}>Get Hired</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/upload-resume`}>Interact with IT Industry Experts</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/bugbounty`}>Bug Bounty Program</a></li>
                        </ul>
                     </div>
                     <div className="col-6">
                        <ul className="nav-links fullbox">
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/tech-news`}>Read Top Tech Updates</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/campus-ambassador`}>Campus Ambassadors</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/company`}>Know About Top Companies</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/codememo`}>Code Memo</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/developer/coding-environment`}>Coding Environment</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/developer/how-to-write-your-code`}>How To Write Your Code</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/developer/create-your-problem`}>Create Your Problem</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_TG_URL}/become-expert`}>Become Skill Expert</a></li>
                        </ul>
                     </div>
                  </div>
                  </div>
                  <div className="col-sm-3 clear-left">
                  <h4 className="dev-head text-uppercase">For Business</h4>
                  <ul className="nav-links">
                     <li>
                        <h5>HIRING SOLUTIONS</h5>
                        <ul>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/challenge`}>Challenges</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/talent-assessment`}>Talent Assessment</a></li>
                        </ul>
                     </li>
                     <li>
                        <h5>ANNUAL EVENTS</h5>
                        <ul>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/codegladiators`}>Code Gladiators</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/geekgoddess`}>Geek Goddess</a></li>
                        </ul>
                     </li>
                     <li>
                        <h5>MARKETING SOLUTIONS</h5>
                        <ul>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/webinars`}>Webinars</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/digital-assets`}>Digital Assets</a></li>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/brand-inventory`}>Brand Inventories</a></li>
                        </ul>
                     </li>
                     <li>
                        <h5>BUSINESS SOLUTIONS</h5>
                        <ul>
                        <li><a target="_blank" href={`${process.env.NEXT_PUBLIC_ENGAGE_URL}/hackathons`}>Hackathons</a></li>
                        </ul>
                     </li>
                  </ul>
                  </div>
               </div>
            </div>
            <div className="col-sm-5">
               <div id="newsletter-form">
                  <div className="d-flex btngroup align-items-center">
                  <span className="flex-shrink-0 fctrl">
                     <img src={`${process.env.NEXT_PUBLIC_TG_URL}/files/contest_upload_files/tg-letter-031024.png`} alt="letter"/>
                  </span>
                  <h4 className="fctrl">Subscribe To Our Newsletter</h4>
                  </div>
                  <form action="#" method="post" className="clearfix">
                  <div className="clearfix form-box">
                     <input type="text" name="email_id" id="subscribe_email" placeholder="Enter your email"/>
                  </div>
                  <div className="mrg16">
                     <input type="button" value="Subscribe TG" className="btn button1 text-uppercase" id="newsletter_subscrption"/>
                  </div>
                  </form>
               </div>
            </div>
         </div>

      </div>
   
      <div className="inner-footer">
            <div className="container">
               <p>Copyright © TechGig (A Product of CoolBoots Media Private Limited) {}.</p>
            </div>
      </div>
   
   </footer>


  );
}