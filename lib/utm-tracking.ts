/**
 * UTM Parameter Tracking Utilities
 * Handles extraction, storage, and retrieval of UTM parameters for marketing attribution
 */

export interface UTMParameters {
  utm_source: string | null;
  utm_medium: string | null;
  utm_campaign: string | null;
}

/**
 * Extract UTM parameters from URL search params
 */
export function extractUTMParameters(searchParams: URLSearchParams): UTMParameters {
  return {
    utm_source: searchParams.get('utm_source') ?? 'DIRECT',
    utm_medium: searchParams.get('utm_medium'),
    utm_campaign: searchParams.get('utm_campaign'),
  };
}

/**
 * Store UTM parameters in cookies with 30-minute expiration
 */
export function storeUTMParametersInCookies(utmParams: UTMParameters): void {
  const expirationTime = 30 * 60 * 1000; // 30 minutes in milliseconds
  const expires = new Date(Date.now() + expirationTime);
  const cookieOptions = `expires=${expires.toUTCString()}; path=/; SameSite=Lax; Secure`;

  // Store each UTM parameter in a separate cookie
  if (utmParams.utm_source) {
    document.cookie = `utm_source=${encodeURIComponent(utmParams.utm_source)}; ${cookieOptions}`;
  }
  if (utmParams.utm_medium) {
    document.cookie = `utm_medium=${encodeURIComponent(utmParams.utm_medium)}; ${cookieOptions}`;
  }
  if (utmParams.utm_campaign) {
    document.cookie = `utm_campaign=${encodeURIComponent(utmParams.utm_campaign)}; ${cookieOptions}`;
  }
}

/**
 * Retrieve UTM parameters from cookies
 */
export function getUTMParametersFromCookies(): UTMParameters {
  const getCookie = (name: string): string | null => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      const cookieValue = parts.pop()?.split(';').shift();
      return cookieValue ? decodeURIComponent(cookieValue) : null;
    }
    return null;
  };

  return {
    utm_source: getCookie('utm_source'),
    utm_medium: getCookie('utm_medium'),
    utm_campaign: getCookie('utm_campaign'),
  };
}

/**
 * Clear UTM parameter cookies
 */
export function clearUTMParameterCookies(): void {
  const pastDate = new Date(0).toUTCString();
  const cookieOptions = `expires=${pastDate}; path=/; SameSite=Lax; Secure`;
  
  document.cookie = `utm_source=; ${cookieOptions}`;
  document.cookie = `utm_medium=; ${cookieOptions}`;
  document.cookie = `utm_campaign=; ${cookieOptions}`;
}

/**
 * Check if any UTM parameters are present in the provided object
 */
export function hasUTMParameters(utmParams: UTMParameters): boolean {
  return !!(utmParams.utm_source || utmParams.utm_medium || utmParams.utm_campaign);
}

/**
 * Server-side cookie parsing utility for Next.js API routes
 */
export function parseUTMParametersFromCookieString(cookieString: string): UTMParameters {
  const cookies: Record<string, string> = {};
  
  if (cookieString) {
    cookieString.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }

  return {
    utm_source: cookies.utm_source || null,
    utm_medium: cookies.utm_medium || null,
    utm_campaign: cookies.utm_campaign || null,
  };
}
