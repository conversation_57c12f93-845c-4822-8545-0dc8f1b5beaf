import NextAuth, { AuthOptions } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { PrismaClient } from '@/lib/generated/prisma'
import { updateUserLoginTime } from '@/lib/user-utils'

const prisma = new PrismaClient()

export const authOptions: AuthOptions = {
  session: {
    strategy: 'jwt'
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({profile}) {
      if(!profile?.email){
        throw new Error('No Profile!');
      }

      try {
        // Check if user already exists in MySQL
        const existingUser = await prisma.user.findUnique({
          where: { email: profile.email }
        });

        // If user doesn't exist, create them
        if (!existingUser) {
          await prisma.user.create({
            data: {
              email: profile.email,
              name: profile.name || null,
              source: 'Google', // Since we're using Google provider
            }
          });
          console.log('New user created in MySQL:', profile.email);
        } else {
          // User exists, update their login time
          await updateUserLoginTime(profile.email);
        }
      } catch (error) {
        console.error('Error saving user to MySQL:', error);
        // Don't block sign-in if database save fails
      }

      return true;
    },
    async jwt({ token, user, profile }) {
      // If this is the first time the JWT is being created (user object exists)
      if (user && profile?.email) {
        try {
          // Fetch the user from database to get the ID
          const dbUser = await prisma.user.findUnique({
            where: { email: profile.email }
          });

          if (dbUser) {
            token.userId = dbUser.id;
          }
        } catch (error) {
          console.error('Error fetching user ID for JWT:', error);
        }
      }
      return token;
    },
    async session({ session, token }) {
      // Add user ID to the session
      if (token.userId && session.user) {
        session.user.id = token.userId as number;
      }
      return session;
    }
  },
  pages: {
    signIn: '/ai-resume/signin',
    error: '/ai-resume/error',
    // add others if needed
  },

}

export default NextAuth(authOptions)
