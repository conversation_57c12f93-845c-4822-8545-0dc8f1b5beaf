/**
 * Temporary Token Utilities
 * For secure PDF generation authentication
 */

import { sign, verify } from 'jsonwebtoken';

const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'fallback-secret';
const TOKEN_EXPIRY = '5m'; // 5 minutes

export interface TempTokenPayload {
  userId: number;
  resumeId: number;
  purpose: 'pdf-generation';
  exp?: number;
}

/**
 * Generate a temporary token for PDF generation
 */
export function generateTempToken(userId: number, resumeId: number): string {
  const payload: TempTokenPayload = {
    userId,
    resumeId,
    purpose: 'pdf-generation'
  };

  return sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRY });
}

/**
 * Verify and decode a temporary token
 */
export function verifyTempToken(token: string): TempTokenPayload | null {
  try {
    const decoded = verify(token, JWT_SECRET) as TempTokenPayload;
    
    // Ensure it's for the correct purpose
    if (decoded.purpose !== 'pdf-generation') {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error('Error verifying temp token:', error);
    return null;
  }
}
