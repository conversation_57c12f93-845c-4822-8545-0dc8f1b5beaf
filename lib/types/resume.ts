/**
 * Resume Data Types
 * Centralized type definitions for resume data structure
 */

export interface PersonalDetails {
  fullName: string;
  email: string;
  phone: string;
  linkedin: string;
  github: string;
  location: string;
}

export interface WorkExperience {
  jobTitle: string;
  companyName: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string;
}

export interface Education {
  degree: string;
  institution: string;
  location: string;
  startDate: string;
  endDate: string;
  description: string;
}

export interface Skill {
  skillType?: "group" | "individual";
  category: string;
  skills: string;
  skill: string;
}

export interface Project {
  projectName: string;
  description: string;
  link: string;
}

export interface Language {
  language: string;
  proficiency: string;
}

export interface Certification {
  certificationName: string;
  issuingOrganization: string;
  issueDate: string;
}

export interface CustomSection {
  sectionTitle: string;
  content: string;
}

export interface ResumeData {
  personalDetails: PersonalDetails;
  objective: string;
  jobTitle: string;
  workExperience: WorkExperience[];
  education: Education[];
  skills: Skill[];
  projects: Project[];
  languages: Language[];
  certifications: Certification[];
  customSections: CustomSection[];
  accentColor?: string;
  fontFamily?: string;
  sectionOrder?: string[];
}

export interface ResumeRecord {
  id: number;
  title?: string;
  user_id: number;
  Name: string | null;
  Email: string | null;
  Phone: string | null;
  LinkedIn: string | null;
  GitHub: string | null;
  Location: string | null;
  Objective: string | null;
  JobTitle: string | null;
  WorkExperience: WorkExperience[] | null;
  Education: Education[] | null;
  Skills: Skill[] | null;
  Projects: Project[] | null;
  Certifications: Certification[] | null;
  Languages: Language[] | null;
  CustomSections: CustomSection[] | null;
  template: string;
  accentColor?: string;
  fontFamily?: string;
  sectionOrder?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateResumeInput {
  title?: string;
  content: ResumeData;
  template?: string;
  accentColor?: string;
  fontFamily?: string;
  sectionOrder?: string[];
}

export interface UpdateResumeInput {
  title?: string;
  content?: ResumeData;
  template?: string;
  accentColor?: string;
  fontFamily?: string;
  sectionOrder?: string[];
}
