/**
 * User Utilities
 * Functions for checking user existence and managing user data
 */

import { PrismaClient } from '@/lib/generated/prisma';
import { UTMParameters } from './utm-tracking';

const prisma = new PrismaClient()

/**
 * Check if a user exists in MySQL database
 */
export async function checkUserExists(email: string): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { email }
    });
    return !!user;
  } catch (error) {
    console.error('Error checking user existence in MySQL:', error);
    // In case of error, assume user exists to avoid duplicate tracking
    return true;
  }
}

/**
 * Get user by email from MySQL database
 */
export async function getUserByEmail(email: string) {
  try {
    return await prisma.user.findUnique({
      where: { email }
    });
  } catch (error) {
    console.error('Error getting user from MySQL:', error);
    return null;
  }
}

/**
 * Update user's updated_at field on login
 */
export async function updateUserLoginTime(email: string): Promise<void> {
  try {
    await prisma.user.update({
      where: { email },
      data: { updatedAt: new Date() }
    });
    console.log('User login time updated for:', email);
  } catch (error) {
    console.error('Error updating user login time:', error);
    // Don't throw error to avoid blocking login
  }
}

/**
 * Save UTM tracking data to the database for a new user
 */
export async function saveUTMTrackingData(email: string, utmParams: UTMParameters, userId?: number): Promise<void> {
  try {
    let finalUserId = userId;

    // If userId is not provided, get it from the email
    if (!finalUserId) {
      const user = await getUserByEmail(email);
      if (!user) {
        throw new Error(`User not found for email: ${email}`);
      }
      finalUserId = user.id;
    }

    const trackingData = {
      user_id: finalUserId,
      utm_source: utmParams.utm_source,
      utm_medium: utmParams.utm_medium,
      utm_campaign: utmParams.utm_campaign,
    };

    const TrackUTM = await prisma.uTMTracking.create({ data: trackingData });
    console.log('UTM mysql created:', TrackUTM);

  } catch (error) {
    console.error('Error saving UTM tracking data:', error);
    throw error;
  }
}

/**
 * Get existing UTM tracking data for a user
 */
export async function getUTMTrackingData(email: string): Promise<UTMParameters | null> {
  try {
    const user = await getUserByEmail(email);
    if (!user) {
      return null;
    }

    const utmTracking = await prisma.uTMTracking.findFirst({
      where: { user_id: user.id },
      orderBy: { created_at: 'desc' }
    });

    if (utmTracking) {
      return {
        utm_source: utmTracking.utm_source,
        utm_medium: utmTracking.utm_medium,
        utm_campaign: utmTracking.utm_campaign,
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting UTM tracking data:', error);
    return null;
  }
}
