"use client";
export default function AboutPage() {


  return (
    <main className="flex min-h-screen flex-col items-center justify-start">
      <div className="container py-10 px-4">
        <h1 className="text-2xl font-bold">MIT License</h1>
        <div className="text-sm break-all">

            <br />
            <div className="font-semibold">Copyright (c) 2024 <PERSON><PERSON>h <PERSON></div>
            <br />
            <p>
            Permission is hereby granted, free of charge, to any person obtaining a copy
            of this software and associated documentation files (the {`"Software"`}), to deal
            in the Software without restriction, including without limitation the rights
            to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
            copies of the Software, and to permit persons to whom the Software is
            furnished to do so, subject to the following conditions:
            </p>
            <br />
            <p>The above copyright notice and this permission notice shall be included in all
            copies or substantial portions of the Software.</p>
            <br />
            <p>
            THE SOFTWARE IS PROVIDED {`"AS IS"`}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
            IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
            FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
            AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
            LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
            OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
            SOFTWARE.
            </p>
        </div>
      </div>
    </main>
  );
}