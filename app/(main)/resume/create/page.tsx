"use client";

import { useSession } from "next-auth/react";
import StepForm from '@/components/resume-builder/StepForm';
import { useRouter } from "next/navigation";

export default function ResumeBuilderPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === "loading") {
    return <div>Loading...</div>; // Optional loading spinner
  }

  // If the user is not signed in and hasn't chosen to proceed
  if (!session) {
    router.push('/');
  }

  // If the user is signed in or chose to proceed without signing in
  return (
    <div className="min-h-screen">
      <StepForm />
    </div>
  );
}
