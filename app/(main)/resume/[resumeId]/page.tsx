import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import ResumeView from './resumeView';
import { getResumeById, convertToResumeData } from '@/lib/resume-utils';
import { ResumeData } from '@/lib/types/resume';



async function getResumeData(resumeId: string): Promise<ResumeData | null> {
  const session = await getServerSession(authOptions);
  try {
    if (!session?.user?.email) {
      return null;
    }

    const resumeIdNum = parseInt(resumeId);
    if (isNaN(resumeIdNum)) {
      return null;
    }

    const resume = await getResumeById(resumeIdNum, session.user.email);

    if (!resume) {
      return null;
    }
    const resumeData = convertToResumeData(resume);

    return resumeData as ResumeData;
  } catch (error) {
    console.error('Error fetching resume:', error);
    return null;
  }
}

export default async function Page({
  params: { resumeId },
}: {
  params: { resumeId: string };
}) {
  const resumeData = await getResumeData(resumeId);
  const session = await getServerSession(authOptions);

  if (!resumeData) {
    return (
      <div className="p-8 text-center dark:bg-gray-800 min-h-[80vh]">
        <p className="text-gray-600 dark:text-gray-400">No resume data found</p>
        <p className="text-sm text-gray-500">ID: {resumeId}</p>
        <p className="text-sm text-gray-500">User: {session?.user?.email}</p>
      </div>
    );
  }

  return <ResumeView resumeData={resumeData} resumeId={resumeId} />;
}