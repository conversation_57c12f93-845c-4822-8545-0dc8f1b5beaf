import Navbar from '@/components/Navbar';
import { Footer } from '@/components/Footer';
import { Toaster } from '@/components/ui/toaster';
import UTMTracker from '@/components/UTMTracker';


export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  
  return (
    <>
        <Navbar />
        <Toaster />
        <UTMTracker />
        {children}
        <Footer />
    </>
  )
}