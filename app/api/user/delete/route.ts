import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { PrismaClient } from '@/lib/generated/prisma';

const prisma = new PrismaClient();

/**
 * DELETE /api/user/delete - Delete user account and all associated data
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { confirmEmail } = body;

    // Require email confirmation for security
    if (confirmEmail !== session.user.email) {
      return NextResponse.json(
        { error: 'Email confirmation does not match' },
        { status: 400 }
      );
    }

    // Get user to ensure they exist
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        resumes: true,
        utmTracking: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Use a transaction to ensure all deletions succeed or fail together
    await prisma.$transaction(async (tx) => {
      // Delete UTM tracking data
      await tx.uTMTracking.deleteMany({
        where: { user_id: user.id }
      });

      // Delete all user's resumes (this will cascade automatically due to foreign key constraints)
      await tx.resume.deleteMany({
        where: { user_id: user.id }
      });

      // Finally, delete the user
      await tx.user.delete({
        where: { id: user.id }
      });
    });

    console.log(`User account deleted: ${session.user.email} (ID: ${user.id})`);

    return NextResponse.json({
      success: true,
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user account:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
