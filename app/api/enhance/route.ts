// route.ts
import { NextResponse } from 'next/server'



export async function POST(req: Request) {
  const OPENAI_API_KEY = process.env.OPENAI_API_KEY
  const OPENAI_MODEL = process.env.OPENAI_MODEL
  const OPENAI_API = process.env.OPENAI_API || "https://api.openai.com/v1/chat/completions"


  if (!OPENAI_API_KEY) {
    return NextResponse.json({ error: 'OPENAI_API_KEY not defined' }, { status: 500 })
  }

  try {
    const { description } = await req.json()

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
    }

    // Only add HTTP-Referer if NEXT_PUBLIC_URL is defined
    if (process.env.NEXT_PUBLIC_URL) {
      headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_URL
    }

    const response = await fetch(OPENAI_API, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model: OPENAI_MODEL,
        messages: [{
          role: "system",
          content: process.env.SYSTEM_PROMPT
        }, {
          role: "user",
          content: process.env.USER_PROMPT?.replace('{job_description}', description)
        }]
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API responded with status: ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.choices?.[0]?.message?.content) {
      throw new Error('Invalid response format from OpenAI API')
    }

    return NextResponse.json({ 
      enhanced: data.choices[0].message.content 
    })
  } catch (error) {
    console.error('Error in enhance API:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to enhance description' },
      { status: 500 }
    )
  }
}