import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { createResume, getUserResumes, convertToResumeData } from '@/lib/resume-utils';
import { CreateResumeInput } from '@/lib/types/resume';

/**
 * GET /api/resumes - Get all resumes for the authenticated user
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const resumes = await getUserResumes(session.user.email);

    // Convert database format to expected ResumeData format for each resume
    const resumesWithContent = resumes.map(resume => ({
      ...resume,
      content: convertToResumeData(resume)
    }));

    return NextResponse.json({
      success: true,
      resumes: resumesWithContent
    });

  } catch (error) {
    console.error('Error fetching resumes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/resumes - Create a new resume
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    console.log('body',body);
    const resumeInput: CreateResumeInput = {
      title: body.title,
      content: body.content,
      template: body.template || 'modern',
      accentColor: body.accentColor,
      fontFamily: body.fontFamily,
      sectionOrder: body.sectionOrder
    };

    // Validate required fields
    if (!resumeInput.content) {
      return NextResponse.json(
        { error: 'Resume content is required' },
        { status: 400 }
      );
    }

    const resume = await createResume(session.user.email, resumeInput);

    // Convert database format to expected ResumeData format
    const resumeWithContent = {
      ...resume,
      content: convertToResumeData(resume)
    };

    return NextResponse.json({
      success: true,
      resume: resumeWithContent,
      message: 'Resume created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
