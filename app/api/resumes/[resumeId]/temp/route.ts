import { NextRequest, NextResponse } from 'next/server';
import { getResumeById, convertToResumeData } from '@/lib/resume-utils';
import { verifyTempToken } from '@/lib/temp-token-utils';
import { PrismaClient } from '@/lib/generated/prisma';

const prisma = new PrismaClient();

/**
 * GET /api/resumes/[resumeId]/temp - Get a specific resume using temporary token
 * This endpoint is used by the PDF generation process
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { resumeId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Temporary token required' },
        { status: 401 }
      );
    }

    // Verify the temporary token
    const tokenPayload = verifyTempToken(token);
    if (!tokenPayload) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const resumeId = parseInt(params.resumeId);
    if (isNaN(resumeId)) {
      return NextResponse.json(
        { error: 'Invalid resume ID' },
        { status: 400 }
      );
    }

    // Verify the token is for this specific resume
    if (tokenPayload.resumeId !== resumeId) {
      return NextResponse.json(
        { error: 'Token not valid for this resume' },
        { status: 403 }
      );
    }

    // Get user email from user ID in token
    const user = await prisma.user.findUnique({
      where: { id: tokenPayload.userId }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Fetch the resume
    const resume = await getResumeById(resumeId, user.email);

    if (!resume) {
      return NextResponse.json(
        { error: 'Resume not found' },
        { status: 404 }
      );
    }

    // Convert database format to expected ResumeData format
    const resumeWithContent = {
      ...resume,
      content: convertToResumeData(resume)
    };

    return NextResponse.json({
      success: true,
      resume: resumeWithContent
    });

  } catch (error) {
    console.error('Error fetching resume with temp token:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
