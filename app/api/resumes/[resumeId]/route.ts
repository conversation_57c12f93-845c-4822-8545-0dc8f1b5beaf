import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { getResumeById, updateResume, deleteResume, convertToResumeData } from '@/lib/resume-utils';
import { UpdateResumeInput } from '@/lib/types/resume';

/**
 * GET /api/resumes/[resumeId] - Get a specific resume
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { resumeId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const resumeId = parseInt(params.resumeId);
    if (isNaN(resumeId)) {
      return NextResponse.json(
        { error: 'Invalid resume ID' },
        { status: 400 }
      );
    }

    const resume = await getResumeById(resumeId, session.user.email);

    if (!resume) {
      return NextResponse.json(
        { error: 'Resume not found' },
        { status: 404 }
      );
    }

    // Convert database format to expected ResumeData format
    const resumeWithContent = {
      ...resume,
      content: convertToResumeData(resume)
    };

    return NextResponse.json({
      success: true,
      resume: resumeWithContent
    });

  } catch (error) {
    console.error('Error fetching resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/resumes/[resumeId] - Update a specific resume
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { resumeId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const resumeId = parseInt(params.resumeId);
    if (isNaN(resumeId)) {
      return NextResponse.json(
        { error: 'Invalid resume ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const updateInput: UpdateResumeInput = {
      title: body.title,
      content: body.content,
      template: body.template,
      accentColor: body.accentColor,
      fontFamily: body.fontFamily,
      sectionOrder: body.sectionOrder
    };

    const updatedResume = await updateResume(resumeId, session.user.email, updateInput);

    if (!updatedResume) {
      return NextResponse.json(
        { error: 'Resume not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      resume: updatedResume,
      message: 'Resume updated successfully'
    });

  } catch (error) {
    console.error('Error updating resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/resumes/[resumeId] - Delete a specific resume
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { resumeId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const resumeId = parseInt(params.resumeId);
    if (isNaN(resumeId)) {
      return NextResponse.json(
        { error: 'Invalid resume ID' },
        { status: 400 }
      );
    }

    const success = await deleteResume(resumeId, session.user.email);

    if (!success) {
      return NextResponse.json(
        { error: 'Resume not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Resume deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
