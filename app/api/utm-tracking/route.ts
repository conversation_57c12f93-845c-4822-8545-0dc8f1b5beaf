import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { parseUTMParametersFromCookieString, hasUTMParameters } from '@/lib/utm-tracking';
import { checkUserExists, saveUTMTrackingData, getUTMTrackingData } from '@/lib/user-utils';

/**
 * API route to handle UTM tracking for authenticated users
 * This endpoint checks if the user is new and saves UTM data if available
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const userEmail = session.user.email;

    // Check if user exists in the database
    const userExists = await checkUserExists(userEmail);

    if (!userExists) {
      return NextResponse.json({
        message: 'User not found in database',
        userExists: false,
        tracked: false
      });
    }

    // Check if UTM tracking data already exists for this user
    const existingUTMData = await getUTMTrackingData(userEmail);

    if (existingUTMData) {
      return NextResponse.json({
        message: 'UTM tracking data already exists for this user',
        userExists: true,
        tracked: false,
        existingUTMData
      });
    }

    // Get UTM parameters from cookies
    const cookieHeader = request.headers.get('cookie') || '';
    const utmParams = parseUTMParametersFromCookieString(cookieHeader);

    // If no UTM parameters found, return early
    if (!hasUTMParameters(utmParams)) {
      return NextResponse.json({
        message: 'No UTM parameters found in cookies',
        userExists: true,
        tracked: false
      });
    }

    // Save UTM tracking data for the new user
    await saveUTMTrackingData(userEmail, utmParams);

    return NextResponse.json({
      message: 'UTM tracking data saved successfully',
      userExists: true,
      tracked: true,
      utmParams
    });

  } catch (error) {
    console.error('Error in UTM tracking API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
