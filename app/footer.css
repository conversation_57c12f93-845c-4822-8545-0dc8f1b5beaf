        #footer {
            font-family: "<PERSON> Sans", sans-serif;
        }

        #footer-related-links a,
        #footer-related-links strong {
            color: #8a858d;
            text-decoration: none;
        }

        #footer ol,
        #footer ul {
            padding: 0;
        }

        #footer h5,
        #footer .h5 {
            color: #19171a;
            font-size: 14px;
            line-height: 21px;
        }

        /* Footer ---------------------- */
        #footer {
            background: #2d1846;
            padding: 40px 0 0;
        }

        /*--- Footer Related Links --- */
        #footer #footer-related-links {
            padding: 16px 0;
        }

        #footer #footer-related-links p {
            font-size: 12px;
            line-height: 22px;
            color: #fff;
            margin-bottom: 8px;
        }

        #footer #footer-related-links p a {
            color: #fff;
        }

        #footer #footer-related-links p a:hover {
            color: #fff;
        }

        /*--- Footer --- */
        #footer h4 {
            margin-bottom: 0px;
            color: #fff;
            font-weight: bold;
            font-size: 21px;
        }

        #footer h4.dev-head {
            font-size: 15px;
            color: #fff;
            font-weight: 900;
        }

        #footer p {
            font-size: 13px;
            color: #C8C5CB;
            line-height: 12px;
            margin-bottom: 0;
        }

        #footer .tg-footer-logo {
            margin-bottom: 14px;
        }

        #footer .inner-footer {
            border-top: solid 1px rgba(255, 255, 255, 0.15);
            padding: 13px 0;
        }

        #footer .footer-error-msg {
            font-size: 12px;
            line-height: 20px;
            margin: 0;
            color: #dadada;
            position: relative;
            margin-bottom: -15px;
            bottom: -12px;
        }

        #footer .footer-sociable {
            display: flex;
            flex-flow: row wrap;
            color: #fff;
            align-items: center;
            justify-content: flex-end;
        }

        #footer .footer-sociable>div {
            margin-right: 16px;
        }

        #footer .footer-sociable a,
        .footersociable a {
            width: 24px;
            display: flex;
            height: 24px;
            border-radius: 50%;
            margin-right: 16px;
            background-color: rgba(255, 255, 255, 0.15);
            text-align: center;
            line-height: 31px;
            padding: 4px;
        }

        #footer .footer-sociable a svg,
        .footersociable a svg {
            vertical-align: middle;
        }

        #footer .footer-sociable a:hover svg path,
        .footersociable a:hover svg path {
            transition: all .1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
            -webkit-transition: all .1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
            -moz-transition: all .1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
            -o-transition: all .1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
            -ms-transition: all .1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
        }

        #footer .footer-sociable a:hover svg path,
        .footersociable a:hover svg path {
            fill: #d7263d;
        }

        #footer .footer-sociable a:last-child,
        .footersociable a:last-child {
            margin: 0;
        }

        #footer .footquick-nav {
            display: flex;
        }

        #footer .nav-links {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        #footer .col-sm-4 .nav-links {
            margin-bottom: 20px;
        }

        #footer .nav-links li {
            margin-bottom: 4px;
        }

        #footer .nav-links li a,
        #footer .footer-more-links ul li a {
            color: #CCC4D6;
            line-height: 16px;
            font-size: 12px;
            text-decoration: none;
        }

        #footer .nav-links li a:hover {
            color: #d7263d;
        }

        #footer .nav-links h5 {
            color: #fff;
            margin-bottom: 4px;
            font-size: 11px;
            font-weight: bold;
        }

        /*--- Newsletter Form --- */
        #newsletter-form {
            border-radius: 3px;
            padding: 24px;
            background: #3D1E63 0% 0% no-repeat padding-box;
        }

        #newsletter-form h6 {
            margin-bottom: 3px;
        }

        #newsletter-form input[type="text"] {
            width: 100%;
            padding-left: 36px;
            background: none;
            border: 0;
            box-shadow: none;
            font-size: 14px;
            font-weight: normal;
            color: #8a858d;
            height: 37px;
            outline: none;
        }

        #newsletter-form input[type="button"] {
            font-size: 16px;
            line-height: 34px;
            width: 170px;
        }

        #newsletter-form input[type="text"]::placeholder {
            color: #787878;
        }

        /*--- Newsletter Form1 --- */
        #newsletter-form1 {
            height: 40px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.15);
        }

        #newsletter-form1 form {
            padding-left: 16px;
            border-radius: 3px;
        }

        #newsletter-form1 input[type="text"] {
            width: 248px;
            float: left;
            background: none;
            padding: 5px 10px 5px 0;
            border: 0;
            box-shadow: none;
            height: 40px;
            outline: none;
        }

        #newsletter-form1 input[type="button"] {
            height: 40px;
            width: 100px;
            float: right;
            border-radius: 0 3px 3px 0;
            line-height: 20px;
        }

        .cloudicon {
            width: 100px;
            border-radius: 14px;
            padding: 4px 9px;
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.06);
            border: solid 1px #5c5cc6;
            background-color: #fff;
            position: absolute;
            top: 0;
            font-size: 12px;
            margin-top: -35px;
            text-align: center;
            left: 0;
            margin-left: -49px;
            height: 26px;
        }

        .form-box {
            background: #F4F4F4 url('https://www.techgig.com/files/contest_upload_files/tg-mail-031024.png') no-repeat 18px center;
            padding: 8px 16px;
            margin-top: 16px;
        }

        .mrgb16 {
            margin-bottom: 16px;
        }

        .mrg16 {
            margin-top: 16px;
        }

        .mrg24 {
            margin-top: 24px;
        }

        .button1 {
            color: rgb(255, 255, 255);
            text-transform: capitalize;
            font-size: 14px;
            line-height: 18px;
            font-weight: bold;
            border-radius: 2px;
            background: rgb(215, 38, 61);
            border-width: 0px;
            border-style: initial;
            border-color: initial;
            border-image: initial;
            padding: 6px 13px;
        }

        .btn {
            box-shadow: none;
            margin-bottom: 0px;
            transition: 0.1s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
            outline: none;
        }

        .text-uppercase {
            text-transform: uppercase !important;
        }

        #footer .footer-sociable a,
        .footersociable a {
            width: 24px;
            display: flex;
            height: 24px;
            border-radius: 50%;
            margin-right: 16px;
            background-color: rgba(255, 255, 255, 0.15);
            text-align: center;
            line-height: 31px;
            padding: 4px;
        }

        .ctrlcenter {
            display: flex;
            align-content: center;
            justify-content: center;
            align-self: center;
            align-items: center;
        }

        #footer .footer-sociable a svg,
        .footersociable a svg {
            vertical-align: middle;
        }

        #footer img {
            max-width: 100%;
            height: auto;
        }

        #footer img,
        #footer svg {
            display: block;
            vertical-align: middle;
        }

        #footer #footer-related-links strong,
        #footer #footer-related-links a {
            color: #fff;
        }

        @media (max-width: 768px) {
            #footer .footer-sociable {
                justify-content: flex-start;
            }

            #footer .nav-links:not(.fullbox) {
                display: grid;
                grid-template-columns: repeat(2, minmax(max-content, 1fr));
            }

            #footer .nav-links li {
                padding: 12px 0;
                line-height: 24px;
            }

            #newsletter-form h4 {
                font-size: 13px;
            }

            #footer p {
                line-height: 18px;
            }

            .footer-link-container, .outer-footer-container, .inner-main-container{
                display: flex;
                flex-direction: column;
            }

             .footer-link-container > div, .outer-footer-container > div, .inner-main-container > div{
                width: 100%;
             }

             .inner-main-container{
                align-items: start !important;
             }
        }