model Resume{
    id Int @id @default(autoincrement())
    title String?
    user_id Int
    user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

    // Resume content stored as JSON
    Name String?
    Email String?
    Phone String?
    LinkedIn String?
    GitHub String?
    Location String?
    Objective String? @db.Text
    JobTitle String?
    WorkExperience Json?
    Education Json?
    Skills Json?
    Projects Json?
    Certifications Json?
    Languages Json?
    CustomSections Json?

    // Template and styling
    template String @default("modern")
    accentColor String?
    fontFamily String?
    sectionOrder Json?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("Resume")
}