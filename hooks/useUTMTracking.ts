/**
 * Custom hook for handling UTM tracking after user authentication
 */

import { useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { clearUTMParameterCookies } from '@/lib/utm-tracking';

export function useUTMTracking() {
  const { data: session, status } = useSession();
  const hasTracked = useRef(false);

  useEffect(() => {
    // Only run when user is authenticated and we haven't tracked yet
    if (status === 'authenticated' && session?.user?.email && !hasTracked.current) {
      handleUTMTracking();
    }
  }, [session, status]);

  const handleUTMTracking = async () => {
    try {
      hasTracked.current = true;

      const response = await fetch(process.env.NEXT_PUBLIC_BASE_URL+'/api/utm-tracking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in the request
      });

      if (!response.ok) {
        console.error('UTM tracking API request failed:', response.statusText);
        return;
      }

      const result = await response.json();
      
      if (result.tracked) {
        console.log('UTM tracking successful:', result.utmParams);
        // Clear UTM cookies after successful tracking
        clearUTMParameterCookies();
      } else {
        console.log('UTM tracking result:', result.message);
      }

    } catch (error) {
      console.error('Error during UTM tracking:', error);
    }
  };

  return {
    isTracking: status === 'authenticated' && !hasTracked.current,
  };
}
